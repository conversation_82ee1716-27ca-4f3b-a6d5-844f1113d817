@startuml 客诉与退款机制泳道图_紧凑版最终

!theme plain
skinparam backgroundColor white
skinparam dpi 150
skinparam swimlaneWidth 120
skinparam swimlaneBorderColor black
skinparam swimlaneBorderThickness 2
skinparam swimlaneTitleBackgroundColor #E8E8E8
skinparam swimlaneTitleFontColor black
skinparam swimlaneTitleFontSize 10
skinparam swimlaneTitleFontStyle bold

skinparam activity {
    BackgroundColor white
    BorderColor black
    BorderThickness 1
    FontColor black
    FontSize 9
    Padding 8
    MinimumWidth 100
}

skinparam diamond {
    BackgroundColor #FFE6CC
    BorderColor #FF9900
    BorderThickness 1
    FontColor black
    FontSize 8
    Padding 6
}

skinparam start {
    BackgroundColor #E6F3FF
    BorderColor #0066CC
    BorderThickness 2
}

skinparam stop {
    BackgroundColor #E6FFE6
    BorderColor #009900
    BorderThickness 2
}

skinparam arrow {
    Color black
    Thickness 1
}

skinparam note {
    BackgroundColor #FFFACD
    BorderColor #DAA520
    FontSize 8
    Padding 4
}

title 客诉与退款机制 - 紧凑版泳道图\n<size:10><color:gray>垂直流向，适合A4打印</color></size>

|👤客户|
start
:遇到问题;
note right: 发现服务异常
:决定投诉;
note right: 选择联系客服

|🐝蜂助手客服|
:接收客诉;
note right: 记录客户信息
:整理诉求;
note right: 分析问题详情

if (问题类型?) then (腾讯视频相关)
    :通知腾讯视频客服;
    note right: 跨部门协作
    
    |📺腾讯视频客服|
    :接收问题通知;
    note right: 获取详细描述
    :分析处理方案;
    note right: 制定解决方案
    
    if (是否需要退款?) then (需要退款)
        :发起退款操作;
        note right: 提交退款申请
        
        |⚙️系统|
        :自动处理退款;
        note right: 执行退款流程
        :更新订单状态;
        note right: 同步状态信息
        
        |📺腾讯视频客服|
        :确认退款完成;
        note right: 验证处理结果
    else (其他处理)
        :执行其他解决方案;
        note right: 补偿或重新开通
    endif
    
    :返回处理结果;
    note right: 整理处理报告
    
    |🐝蜂助手客服|
    :接收处理结果;
    note right: 获取处理结果
    
else (一般问题)
    :直接处理;
    note right: 标准流程解决
endif

:联系客户告知结果;
note right: 电话或消息通知

|👤客户|
:接收处理结果;
note right: 了解处理情况
:确认问题解决;
note right: 验证问题解决

|🐝蜂助手客服|
:完成客诉处理;
note right: 记录结果闭环
stop

@enduml
