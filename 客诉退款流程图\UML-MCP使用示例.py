#!/usr/bin/env python3
"""
UML-MCP Server 使用示例
演示如何使用 UML-MCP 生成各种类型的图表
"""

import sys
import os
from pathlib import Path

# 添加 UML-MCP 路径
uml_mcp_path = Path(__file__).parent.parent / "uml-mcp"
sys.path.insert(0, str(uml_mcp_path))

try:
    from mcp_core.tools.diagram_tools import (
        generate_sequence_diagram,
        generate_class_diagram,
        generate_activity_diagram,
        generate_mermaid_diagram
    )
    print("✅ UML-MCP 模块导入成功")
except ImportError as e:
    print(f"❌ 导入失败: {e}")
    print(f"请确保 UML-MCP 已正确安装在: {uml_mcp_path}")
    sys.exit(1)

def generate_customer_complaint_sequence():
    """生成客诉退款时序图"""
    
    plantuml_code = """
@startuml 客诉与退款机制时序图

!theme plain
skinparam backgroundColor white
skinparam swimlaneWidth 200
skinparam swimlaneBorderColor black
skinparam swimlaneBorderThickness 2

title 客诉与退款机制 - 时序图

participant 客户 as Customer
participant 蜂助手客服 as BeeService  
participant 腾讯视频客服 as TencentService
participant 系统 as System

Customer -> BeeService: 提出客诉问题
BeeService -> BeeService: 整理客户诉求

alt 涉及腾讯视频权益查询和联合会员退款
    BeeService -> TencentService: 通过在线文档通知问题
    TencentService -> TencentService: 处理客诉问题
    
    alt 需要退款
        TencentService -> System: 操作订单退款
        System -> BeeService: 自动返回订单状态
    end
    
    TencentService -> BeeService: 返回处理结果
else 其他问题
    BeeService -> BeeService: 直接处理
end

BeeService -> Customer: 联系客户告知处理结果

@enduml
"""
    
    output_dir = str(Path("../uml-output").absolute())
    
    print("🚀 生成客诉退款时序图...")
    result = generate_sequence_diagram(code=plantuml_code, output_dir=output_dir)
    print(f"✅ 时序图生成成功: {result.get('local_path', 'Unknown')}")
    return result

def generate_customer_complaint_class():
    """生成客诉系统类图"""
    
    plantuml_code = """
@startuml 客诉系统类图

!theme plain
skinparam backgroundColor white
skinparam classBackgroundColor white
skinparam classBorderColor black

title 客诉与退款系统 - 类图

class Customer {
    -id: String
    -name: String
    -phone: String
    -email: String
    +submitComplaint(): Complaint
    +receiveResult(): void
}

class Complaint {
    -id: String
    -customerId: String
    -description: String
    -type: ComplaintType
    -status: ComplaintStatus
    -createTime: DateTime
    +process(): void
    +updateStatus(): void
}

class BeeCustomerService {
    -id: String
    -name: String
    +receiveComplaint(): void
    +analyzeComplaint(): ComplaintType
    +processDirectly(): void
    +forwardToTencent(): void
    +contactCustomer(): void
}

class TencentVideoService {
    -id: String
    -name: String
    +receiveNotification(): void
    +processComplaint(): void
    +executeRefund(): RefundResult
    +returnResult(): void
}

class RefundSystem {
    +processRefund(): RefundResult
    +updateOrderStatus(): void
    +notifyServices(): void
}

enum ComplaintType {
    GENERAL
    TENCENT_VIDEO_RELATED
    REFUND_REQUEST
}

enum ComplaintStatus {
    SUBMITTED
    PROCESSING
    FORWARDED
    RESOLVED
    CLOSED
}

Customer "1" -- "many" Complaint : submits
BeeCustomerService "1" -- "many" Complaint : handles
TencentVideoService "1" -- "many" Complaint : processes
RefundSystem "1" -- "many" Complaint : refunds

@enduml
"""
    
    output_dir = str(Path("../uml-output").absolute())
    
    print("🚀 生成客诉系统类图...")
    result = generate_class_diagram(code=plantuml_code, output_dir=output_dir)
    print(f"✅ 类图生成成功: {result.get('local_path', 'Unknown')}")
    return result

def generate_customer_complaint_activity():
    """生成客诉处理活动图"""
    
    plantuml_code = """
@startuml 客诉处理活动图

!theme plain
skinparam backgroundColor white

title 客诉处理流程 - 活动图

start

:客户遇到问题;
:客户提交客诉;
:蜂助手客服接收;
:整理客户诉求;

if (问题类型?) then (腾讯视频相关)
    :通知腾讯视频客服;
    :腾讯视频客服处理;
    
    if (是否需要退款?) then (需要)
        :执行退款操作;
        :系统自动处理;
        :返回订单状态;
    else (不需要)
        :其他处理方式;
    endif
    
    :返回处理结果;
    :蜂助手接收结果;
    
else (一般问题)
    :蜂助手直接处理;
endif

:联系客户告知结果;
:客户确认问题解决;

stop

@enduml
"""
    
    output_dir = str(Path("../uml-output").absolute())
    
    print("🚀 生成客诉处理活动图...")
    result = generate_activity_diagram(code=plantuml_code, output_dir=output_dir)
    print(f"✅ 活动图生成成功: {result.get('local_path', 'Unknown')}")
    return result

def generate_mermaid_swimlane():
    """生成 Mermaid 泳道图"""
    
    mermaid_code = """
flowchart TD
    subgraph 客户泳道 ["👤 客户"]
        A1[遇到问题]
        A2[提出客诉]
        A3[等待处理结果]
        A4[接收处理结果]
        A5[确认问题解决]
    end
    
    subgraph 蜂助手客服泳道 ["🐝 蜂助手客服"]
        B1[接收客诉]
        B2[整理客户诉求]
        B3{判断问题类型}
        B4[直接处理一般问题]
        B5[通知腾讯视频客服]
        B6[接收处理结果]
        B7[联系客户告知结果]
    end
    
    subgraph 腾讯视频客服泳道 ["📺 腾讯视频客服"]
        C1[接收问题通知]
        C2[处理客诉问题]
        C3{是否需要退款}
        C4[执行退款操作]
        C5[其他处理方式]
        C6[返回处理结果]
    end
    
    subgraph 系统泳道 ["⚙️ 系统"]
        D1[自动处理退款]
        D2[返回订单状态]
    end
    
    A1 --> A2
    A2 --> B1
    B1 --> B2
    B2 --> B3
    
    B3 -->|一般问题| B4
    B3 -->|腾讯视频相关| B5
    
    B4 --> B7
    B5 --> C1
    C1 --> C2
    C2 --> C3
    
    C3 -->|需要退款| C4
    C3 -->|其他处理| C5
    
    C4 --> D1
    D1 --> D2
    D2 --> B6
    C5 --> C6
    C6 --> B6
    
    B6 --> B7
    B7 --> A3
    A3 --> A4
    A4 --> A5
    
    style 客户泳道 fill:#e3f2fd
    style 蜂助手客服泳道 fill:#fff3e0
    style 腾讯视频客服泳道 fill:#f3e5f5
    style 系统泳道 fill:#e8f5e8
"""
    
    output_dir = str(Path("../uml-output").absolute())
    
    print("🚀 生成 Mermaid 泳道图...")
    result = generate_mermaid_diagram(code=mermaid_code, output_dir=output_dir)
    print(f"✅ Mermaid 图生成成功: {result.get('local_path', 'Unknown')}")
    return result

def main():
    """主函数 - 生成所有图表"""
    
    print("🎯 UML-MCP Server 客诉退款流程图生成示例")
    print("=" * 50)
    
    # 确保输出目录存在
    output_dir = Path("../uml-output")
    output_dir.mkdir(exist_ok=True)
    
    try:
        # 生成各种图表
        results = []
        
        results.append(generate_customer_complaint_sequence())
        results.append(generate_customer_complaint_class())
        results.append(generate_customer_complaint_activity())
        results.append(generate_mermaid_swimlane())
        
        print("\n" + "=" * 50)
        print("🎉 所有图表生成完成！")
        print(f"📁 输出目录: {output_dir.absolute()}")
        
        print("\n📊 生成的文件:")
        for i, result in enumerate(results, 1):
            if result and 'local_path' in result:
                file_path = Path(result['local_path'])
                if file_path.exists():
                    print(f"  {i}. {file_path.name}")
                    if 'playground' in result:
                        print(f"     🔗 在线查看: {result['playground']}")
        
        print("\n💡 提示:")
        print("- SVG 文件可以在浏览器中直接打开")
        print("- 可以使用在线 PlantUML 编辑器进一步修改")
        print("- Mermaid 图表可以在 https://mermaid.live/ 中编辑")
        
    except Exception as e:
        print(f"❌ 生成过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
