#!/usr/bin/env python3
"""
测试 UML-MCP Server 的 PlantUML 功能
"""

import os
import sys
import tempfile
from pathlib import Path

# 添加项目路径
sys.path.insert(0, str(Path(__file__).parent))

try:
    from mcp_core.tools.diagram_tools import generate_sequence_diagram
    print("✅ 成功导入 UML-MCP 模块")
except ImportError as e:
    print(f"❌ 导入失败: {e}")
    sys.exit(1)

def test_plantuml_generation():
    """测试 PlantUML 时序图生成"""
    
    # 客诉退款流程的 PlantUML 代码
    plantuml_code = """
@startuml 客诉与退款机制时序图

participant 客户 as Customer
participant 蜂助手客服 as BeeService  
participant 腾讯视频客服 as TencentService
participant 系统 as System

Customer -> BeeService: 提出客诉问题
BeeService -> BeeService: 整理客户诉求

alt 涉及腾讯视频权益查询和联合会员退款
    BeeService -> TencentService: 通过在线文档通知问题
    TencentService -> TencentService: 处理客诉问题
    
    alt 需要退款
        TencentService -> System: 操作订单退款
        System -> BeeService: 自动返回订单状态
    end
    
    TencentService -> BeeService: 返回处理结果
else 其他问题
    BeeService -> BeeService: 直接处理
end

BeeService -> Customer: 联系客户告知处理结果

@enduml
"""
    
    # 输出目录
    output_dir = Path("../uml-output")
    output_dir.mkdir(exist_ok=True)
    
    print("🚀 开始测试 PlantUML 生成...")
    print(f"📁 输出目录: {output_dir.absolute()}")
    
    try:
        # 调用生成函数
        result = generate_sequence_diagram(
            code=plantuml_code,
            output_dir=str(output_dir.absolute())
        )
        
        print("✅ PlantUML 图表生成成功!")
        print(f"📊 结果: {result}")
        
        # 检查生成的文件
        for file in output_dir.glob("*.png"):
            print(f"📄 生成文件: {file}")
            
    except Exception as e:
        print(f"❌ 生成失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_plantuml_generation()
