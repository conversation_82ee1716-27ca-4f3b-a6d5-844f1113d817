# 客诉与退款机制 - PlantUML代码文档

## 📋 文档说明

本文档包含了客诉与退款机制的两个核心图表的完整PlantUML代码：
- **时序图**：展示各方交互的时间顺序和消息传递
- **泳道图**：展示各部门职责分工和跨职能协作

## 🔄 时序图代码

### 特点
- 清晰的时间轴和消息传递
- 分阶段展示（问题发现、分析分发、退款处理、结果反馈）
- 详细的注释说明
- 专业的样式配置

### 完整代码

```plantuml
@startuml 客诉与退款机制时序图

!theme plain
skinparam backgroundColor white
skinparam participant {
    BackgroundColor white
    BorderColor black
    BorderThickness 2
}

skinparam sequence {
    ArrowColor black
    ArrowThickness 2
    LifeLineBackgroundColor white
    LifeLineBorderColor black
    LifeLineBorderThickness 2
}

skinparam note {
    BackgroundColor #FFFACD
    BorderColor #DAA520
}

title 客诉与退款机制 - 时序图\n<size:12><color:gray>展示各方交互的时间顺序和消息传递</color></size>

participant "👤\n客户" as Customer
participant "🐝\n蜂助手客服" as BeeService  
participant "📺\n腾讯视频客服" as TencentService
participant "⚙️\n系统" as System

== 问题发现与提交 ==
Customer -> BeeService: 1. 提出客诉问题
note right: 客户遇到问题后\n主动联系客服

BeeService -> BeeService: 2. 整理客户诉求
note right: 记录问题详情\n分析问题类型

== 问题分析与分发 ==
alt 涉及腾讯视频权益查询和联合会员退款
    BeeService -> TencentService: 3. 通过在线文档通知问题
    note right: 跨部门协作\n信息准确传递
    
    TencentService -> TencentService: 4. 处理客诉问题
    note right: 专业团队\n深入分析问题
    
    == 退款处理 ==
    alt 需要退款
        TencentService -> System: 5. 操作订单退款
        note right: 在系统中\n执行退款操作
        
        System -> BeeService: 6. 自动返回订单状态
        note right: 系统自动化\n状态同步
    end
    
    TencentService -> BeeService: 7. 返回处理结果
    note right: 处理结果\n反馈给蜂助手
    
else 其他问题
    BeeService -> BeeService: 3. 直接处理
    note right: 标准流程\n快速解决
end

== 结果反馈 ==
BeeService -> Customer: 8. 联系客户告知处理结果
note right: 完成闭环\n客户满意度跟踪

@enduml
```

## 🏊‍♀️ 泳道图代码

### 特点
- 清晰的职责分工和泳道边界
- 完整的业务流程展示
- 丰富的注释和说明
- 专业的配色方案

### 完整代码

```plantuml
@startuml 客诉与退款机制泳道图

!theme plain
skinparam backgroundColor white
skinparam swimlaneWidth 220
skinparam swimlaneBorderColor black
skinparam swimlaneBorderThickness 3
skinparam swimlaneTitleBackgroundColor #E8E8E8
skinparam swimlaneTitleFontColor black
skinparam swimlaneTitleFontSize 14
skinparam swimlaneTitleFontStyle bold

skinparam activity {
    BackgroundColor white
    BorderColor black
    BorderThickness 2
    FontColor black
    FontSize 12
}

skinparam diamond {
    BackgroundColor #FFE6CC
    BorderColor #FF9900
    BorderThickness 2
    FontColor black
    FontSize 11
}

skinparam start {
    BackgroundColor #E6F3FF
    BorderColor #0066CC
    BorderThickness 3
}

skinparam stop {
    BackgroundColor #E6FFE6
    BorderColor #009900
    BorderThickness 3
}

skinparam arrow {
    Color black
    Thickness 2
}

title 客诉与退款机制 - 泳道图\n<size:12><color:gray>展示各部门职责分工和跨职能协作</color></size>

|👤 客户|
start
:🔴 遇到问题;
note right: 发现服务异常\n或权益问题
:📞 决定投诉;
note right: 选择联系\n客服解决

|🐝 蜂助手客服|
:📋 接收客诉;
note right: 记录客户\n基本信息
:📝 整理客户诉求;
note right: 分析问题详情\n和影响范围

if (❓ 问题类型判断) then (腾讯视频相关)
    :📤 通知腾讯视频客服;
    note right: 通过在线文档\n传递问题信息
    
    |📺 腾讯视频客服|
    :📥 接收问题通知;
    note right: 获取详细\n问题描述
    :🔧 分析处理方案;
    note right: 专业团队\n制定解决方案
    
    if (💳 是否需要退款?) then (需要退款)
        :💰 发起退款操作;
        note right: 在系统中\n提交退款申请
        
        |⚙️ 系统|
        :🔄 自动处理退款;
        note right: 系统自动执行\n退款流程
        :📊 更新订单状态;
        note right: 同步状态信息\n到各个系统
        
        |📺 腾讯视频客服|
        :✅ 确认退款完成;
        note right: 验证退款\n处理结果
    else (其他处理)
        :🔧 执行其他解决方案;
        note right: 补偿、重新开通\n或其他处理方式
    endif
    
    :📤 准备处理结果;
    note right: 整理完整的\n处理报告
    
    |🐝 蜂助手客服|
    :📥 接收处理结果;
    note right: 获取腾讯视频\n的处理结果
    
else (一般问题)
    :✅ 直接处理;
    note right: 按标准流程\n快速解决
endif

:📞 联系客户告知结果;
note right: 电话或消息\n通知处理结果

|👤 客户|
:📨 接收处理结果;
note right: 了解最终\n处理情况
:✅ 确认问题解决;
note right: 验证问题是否\n彻底解决

|🐝 蜂助手客服|
:🎯 完成客诉处理;
note right: 记录处理结果\n闭环完成
stop

@enduml
```

## 🎨 样式配置说明

### 通用样式
- `!theme plain`: 使用简洁主题
- `skinparam backgroundColor white`: 白色背景
- 边框颜色统一使用黑色 (`black`)
- 边框粗细：2-3像素

### 时序图特有样式
- `participant`: 参与者样式配置
- `sequence`: 时序图特有的箭头和生命线样式
- `note`: 注释框样式（淡黄色背景）

### 泳道图特有样式
- `swimlaneWidth`: 泳道宽度 220px
- `swimlaneBorderThickness`: 泳道边框粗细 3px
- `activity`: 活动框样式
- `diamond`: 决策框样式（橙色背景）
- `start/stop`: 开始/结束节点样式

## 💡 使用建议

### 本地使用
1. 复制代码到 `.puml` 文件
2. 使用 PlantUML 工具生成图片
3. 或使用 VS Code 的 PlantUML 插件

### 在线使用
1. 访问 http://www.plantuml.com/plantuml/
2. 粘贴代码到编辑器
3. 生成并下载图片

### 自定义修改
- 修改 `skinparam` 参数调整样式
- 修改颜色代码改变配色方案
- 添加或删除 `note` 来调整注释
- 调整 `swimlaneWidth` 改变泳道宽度

## 📊 图表对比

| 特征 | 时序图 | 泳道图 |
|------|--------|--------|
| **主要用途** | 展示时间顺序和消息传递 | 展示职责分工和业务流程 |
| **适合受众** | 技术人员、开发团队 | 业务人员、管理层 |
| **关注重点** | 何时发生交互 | 谁负责什么工作 |
| **优化指导** | 系统性能、API设计 | 流程效率、职责分工 |

建议根据不同的使用场景选择合适的图表类型！
