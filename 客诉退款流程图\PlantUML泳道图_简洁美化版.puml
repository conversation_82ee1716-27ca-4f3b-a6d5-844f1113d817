@startuml 客诉与退款机制泳道图

!theme plain
skinparam backgroundColor white
skinparam swimlaneWidth 200
skinparam swimlaneBorderColor black
skinparam swimlaneBorderThickness 2
skinparam swimlaneTitleBackgroundColor #E8E8E8
skinparam swimlaneTitleFontColor black
skinparam swimlaneTitleFontSize 12
skinparam swimlaneTitleFontStyle bold

skinparam activity {
    BackgroundColor white
    BorderColor black
    BorderThickness 1
    FontColor black
    FontSize 11
}

skinparam diamond {
    BackgroundColor #FFE6CC
    BorderColor #FF9900
    BorderThickness 2
    FontColor black
    FontSize 10
}

skinparam start {
    BackgroundColor #E6F3FF
    BorderColor #0066CC
    BorderThickness 2
}

skinparam stop {
    BackgroundColor #E6FFE6
    BorderColor #009900
    BorderThickness 2
}

skinparam arrow {
    Color black
    Thickness 2
}

title 客诉与退款机制 - 跨职能泳道图

|👤 客户|
start
:遇到问题;
:决定投诉;
:联系蜂助手客服;

|🐝 蜂助手客服|
:接收客诉;
:整理客户诉求;

if (问题类型判断?) then (腾讯视频相关)
    :通知腾讯视频客服;
    
    |📺 腾讯视频客服|
    :接收问题通知;
    :处理客诉问题;
    
    if (是否需要退款?) then (需要)
        :执行退款操作;
        
        |⚙️ 系统|
        :自动处理退款;
        :返回订单状态;
        
        |📺 腾讯视频客服|
    else (不需要)
        :其他处理方式;
    endif
    
    :返回处理结果;
    
    |🐝 蜂助手客服|
    :接收处理结果;
    
else (一般问题)
    :直接处理;
endif

:联系客户告知结果;

|👤 客户|
:接收处理结果;
:确认问题解决;
stop

@enduml

' 使用说明：
' 1. 在线生成: http://www.plantuml.com/plantuml/
' 2. VS Code插件: PlantUML
' 3. 命令行: java -jar plantuml.jar 文件名.puml
' 4. 这个版本语法简洁，兼容性好，美观实用
