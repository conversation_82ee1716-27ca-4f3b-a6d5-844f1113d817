@startuml 客诉与退款机制_表格式泳道图

!theme plain

' 全局样式设置
skinparam backgroundColor white
skinparam defaultFontName "Microsoft YaHei"
skinparam defaultFontSize 11

' 泳道样式 - 完整边界线
skinparam swimlaneWidth 200
skinparam swimlaneBorderColor #000000
skinparam swimlaneBorderThickness 2
skinparam swimlaneTitleBackgroundColor #E8E8E8
skinparam swimlaneTitleFontColor #000000
skinparam swimlaneTitleFontSize 12
skinparam swimlaneTitleFontStyle bold

' 分区样式 - 水平分隔线
skinparam partition {
    BackgroundColor #F8F8F8
    BorderColor #000000
    BorderThickness 2
    FontColor #000000
    FontSize 11
    FontStyle bold
}

' 活动框样式
skinparam activity {
    BackgroundColor #FFFFFF
    BorderColor #333333
    BorderThickness 1
    FontColor #000000
    FontSize 10
}

' 决策框样式
skinparam diamond {
    BackgroundColor #FFE6CC
    BorderColor #FF9900
    BorderThickness 2
    FontColor #000000
    FontSize 9
}

' 开始/结束样式
skinparam start {
    BackgroundColor #E6F3FF
    BorderColor #0066CC
    BorderThickness 2
}

skinparam stop {
    BackgroundColor #E6FFE6
    BorderColor #009900
    BorderThickness 2
}

' 箭头样式
skinparam arrow {
    Color #000000
    Thickness 1
}

title <size:16><b>客诉与退款机制 - 表格式泳道图</b></size>

' 使用partition创建水平分隔的阶段
partition "阶段1：问题发现" #F0F8FF {
    |👤 客户|
    start
    :遇到问题;
    :决定投诉;
    
    |🐝 蜂助手客服|
    ' 空白区域
    
    |📺 腾讯视频客服|
    ' 空白区域
    
    |⚙️ 系统|
    ' 空白区域
}

partition "阶段2：客诉提交" #FFF8F0 {
    |👤 客户|
    :联系蜂助手客服;
    :描述问题详情;
    
    |🐝 蜂助手客服|
    :接收客诉;
    :整理客户诉求;
    
    |📺 腾讯视频客服|
    ' 空白区域
    
    |⚙️ 系统|
    ' 空白区域
}

partition "阶段3：问题分析" #F8F0FF {
    |👤 客户|
    :等待处理结果;
    
    |🐝 蜂助手客服|
    if (问题类型判断?) then (腾讯视频相关)
        :通知腾讯视频客服;
    else (一般问题)
        :直接处理;
        jump to 反馈结果
    endif
    
    |📺 腾讯视频客服|
    :接收问题通知;
    :分析处理方案;
    
    |⚙️ 系统|
    ' 空白区域
}

partition "阶段4：处理执行" #F0FFF0 {
    |👤 客户|
    ' 空白区域
    
    |🐝 蜂助手客服|
    ' 等待处理结果
    
    |📺 腾讯视频客服|
    if (是否需要退款?) then (需要)
        :执行退款操作;
    else (不需要)
        :其他处理方式;
    endif
    
    |⚙️ 系统|
    :自动处理退款;
    :返回订单状态;
}

partition "阶段5：结果反馈" #FFF0F8 {
    |👤 客户|
    label 反馈结果
    :接收处理结果;
    :确认问题解决;
    stop
    
    |🐝 蜂助手客服|
    :接收处理结果;
    :联系客户告知结果;
    :客诉处理完成;
    
    |📺 腾讯视频客服|
    :返回处理结果;
    
    |⚙️ 系统|
    ' 空白区域
}

@enduml

' 使用说明：
' 1. 每个partition代表一个处理阶段，形成水平分隔线
' 2. 每个泳道代表一个参与方，形成垂直分隔线
' 3. 这样形成了完整的表格式布局
' 4. 在线生成: http://www.plantuml.com/plantuml/
' 5. 导出时选择PNG或SVG格式获得最佳效果
