<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>客诉与退款机制 - UML图表查看器</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }
        
        .header h1 {
            margin: 0;
            font-size: 24px;
        }
        
        .header p {
            margin: 10px 0 0 0;
            opacity: 0.9;
        }
        
        .tabs {
            display: flex;
            background: #f8f9fa;
            border-bottom: 1px solid #dee2e6;
        }
        
        .tab {
            flex: 1;
            padding: 15px;
            text-align: center;
            cursor: pointer;
            border: none;
            background: none;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        
        .tab:hover {
            background: #e9ecef;
        }
        
        .tab.active {
            background: white;
            border-bottom: 3px solid #667eea;
            color: #667eea;
        }
        
        .content {
            padding: 20px;
        }
        
        .diagram-container {
            text-align: center;
            margin: 20px 0;
        }
        
        .diagram-container svg {
            max-width: 100%;
            height: auto;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            background: white;
        }
        
        .info-box {
            background: #e3f2fd;
            border: 1px solid #2196f3;
            border-radius: 4px;
            padding: 15px;
            margin: 20px 0;
        }
        
        .info-box h3 {
            margin: 0 0 10px 0;
            color: #1976d2;
        }
        
        .file-list {
            background: #f8f9fa;
            border-radius: 4px;
            padding: 15px;
            margin: 20px 0;
        }
        
        .file-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #dee2e6;
        }
        
        .file-item:last-child {
            border-bottom: none;
        }
        
        .file-name {
            font-weight: 500;
        }
        
        .file-size {
            color: #6c757d;
            font-size: 12px;
        }
        
        .hidden {
            display: none;
        }
        
        .loading {
            text-align: center;
            padding: 40px;
            color: #6c757d;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎯 客诉与退款机制 - UML图表查看器</h1>
            <p>专业的PlantUML泳道图 - 由UML-MCP Server生成</p>
        </div>
        
        <div class="tabs">
            <button class="tab active" onclick="showTab('sequence')">时序图</button>
            <button class="tab" onclick="showTab('class')">类图</button>
            <button class="tab" onclick="showTab('activity')">活动图</button>
            <button class="tab" onclick="showTab('mermaid')">Mermaid泳道图</button>
        </div>
        
        <div class="content">
            <!-- 时序图 -->
            <div id="sequence" class="tab-content">
                <div class="info-box">
                    <h3>📊 时序图说明</h3>
                    <p>展示客户、蜂助手客服、腾讯视频客服和系统之间的交互时序，清晰显示各方在客诉处理过程中的职责和协作关系。</p>
                </div>
                
                <div class="diagram-container">
                    <div class="loading" id="sequence-loading">正在加载时序图...</div>
                    <div id="sequence-diagram"></div>
                </div>
            </div>
            
            <!-- 类图 -->
            <div id="class" class="tab-content hidden">
                <div class="info-box">
                    <h3>🏗️ 类图说明</h3>
                    <p>展示客诉系统的核心类结构，包括客户、投诉、客服等实体及其关系，为系统设计提供架构参考。</p>
                </div>
                
                <div class="diagram-container">
                    <div class="loading" id="class-loading">正在加载类图...</div>
                    <div id="class-diagram"></div>
                </div>
            </div>
            
            <!-- 活动图 -->
            <div id="activity" class="tab-content hidden">
                <div class="info-box">
                    <h3>🔄 活动图说明</h3>
                    <p>展示客诉处理的完整业务流程，包括决策点和不同的处理路径，便于理解业务逻辑。</p>
                </div>
                
                <div class="diagram-container">
                    <div class="loading" id="activity-loading">正在加载活动图...</div>
                    <div id="activity-diagram"></div>
                </div>
            </div>
            
            <!-- Mermaid泳道图 -->
            <div id="mermaid" class="tab-content hidden">
                <div class="info-box">
                    <h3>🏊‍♀️ Mermaid泳道图说明</h3>
                    <p>使用Mermaid语法创建的跨职能泳道图，清晰展示各部门的职责边界和协作流程。</p>
                </div>
                
                <div class="diagram-container">
                    <div class="loading" id="mermaid-loading">正在加载Mermaid图...</div>
                    <div id="mermaid-diagram"></div>
                </div>
            </div>
            
            <div class="file-list">
                <h3>📁 生成的文件</h3>
                <div class="file-item">
                    <span class="file-name">sequence_20250804180458.svg</span>
                    <span class="file-size">时序图 - SVG格式</span>
                </div>
                <div class="file-item">
                    <span class="file-name">class_20250804180500.svg</span>
                    <span class="file-size">类图 - SVG格式</span>
                </div>
                <div class="file-item">
                    <span class="file-name">activity_20250804180501.svg</span>
                    <span class="file-size">活动图 - SVG格式</span>
                </div>
                <div class="file-item">
                    <span class="file-name">mermaid_20250804180502.svg</span>
                    <span class="file-size">Mermaid泳道图 - SVG格式</span>
                </div>
            </div>
            
            <div class="info-box">
                <h3>💡 使用说明</h3>
                <ul>
                    <li>所有图表都是由UML-MCP Server自动生成的高质量SVG格式</li>
                    <li>图表支持缩放和平移，可以查看细节</li>
                    <li>SVG文件可以直接在浏览器中打开或导入到其他工具中</li>
                    <li>如需编辑，可以使用PlantUML在线编辑器或Mermaid Live Editor</li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        // 切换标签页
        function showTab(tabName) {
            // 隐藏所有内容
            const contents = document.querySelectorAll('.tab-content');
            contents.forEach(content => content.classList.add('hidden'));
            
            // 移除所有活动状态
            const tabs = document.querySelectorAll('.tab');
            tabs.forEach(tab => tab.classList.remove('active'));
            
            // 显示选中的内容
            document.getElementById(tabName).classList.remove('hidden');
            event.target.classList.add('active');
            
            // 加载对应的图表
            loadDiagram(tabName);
        }
        
        // 加载图表
        async function loadDiagram(type) {
            const diagramContainer = document.getElementById(`${type}-diagram`);
            const loadingElement = document.getElementById(`${type}-loading`);
            
            if (diagramContainer.innerHTML.trim() !== '') {
                return; // 已经加载过了
            }
            
            try {
                // 根据类型确定文件名
                let fileName;
                switch(type) {
                    case 'sequence':
                        fileName = '../uml-output/sequence_20250804180458.svg';
                        break;
                    case 'class':
                        fileName = '../uml-output/class_20250804180500.svg';
                        break;
                    case 'activity':
                        fileName = '../uml-output/activity_20250804180501.svg';
                        break;
                    case 'mermaid':
                        fileName = '../uml-output/mermaid_20250804180502.svg';
                        break;
                }
                
                const response = await fetch(fileName);
                if (response.ok) {
                    const svgContent = await response.text();
                    diagramContainer.innerHTML = svgContent;
                    loadingElement.style.display = 'none';
                } else {
                    throw new Error('文件加载失败');
                }
            } catch (error) {
                loadingElement.innerHTML = `❌ 加载失败: ${error.message}<br><small>请确保SVG文件存在于uml-output目录中</small>`;
            }
        }
        
        // 页面加载时自动加载第一个图表
        document.addEventListener('DOMContentLoaded', function() {
            loadDiagram('sequence');
        });
    </script>
</body>
</html>
