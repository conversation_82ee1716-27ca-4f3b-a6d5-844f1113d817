@startuml 客诉与退款机制时序图_A4优化版

!theme plain
skinparam backgroundColor white
skinparam dpi 150
skinparam maxMessageSize 150

skinparam participant {
    BackgroundColor white
    BorderColor black
    BorderThickness 2
    FontSize 11
    Padding 8
}

skinparam sequence {
    ArrowColor black
    ArrowThickness 2
    LifeLineBackgroundColor white
    LifeLineBorderColor black
    LifeLineBorderThickness 2
    ParticipantPadding 20
    BoxPadding 10
}

skinparam note {
    BackgroundColor #FFFACD
    BorderColor #DAA520
    FontSize 10
}

title 客诉与退款机制 - 时序图\n<size:11><color:gray>展示各方交互的时间顺序和消息传递</color></size>

participant "👤\n客户" as C
participant "🐝\n蜂助手\n客服" as B
participant "📺\n腾讯视频\n客服" as T
participant "⚙️\n系统" as S

== 问题发现与提交 ==
C -> B: 1. 提出客诉问题
note right: 客户遇到问题后\n主动联系客服

B -> B: 2. 整理客户诉求
note right: 记录问题详情\n分析问题类型

== 问题分析与分发 ==
alt 涉及腾讯视频权益查询和联合会员退款
    B -> T: 3. 通过在线文档\n通知问题
    note right: 跨部门协作\n信息准确传递
    
    T -> T: 4. 处理客诉问题
    note right: 专业团队\n深入分析问题
    
    == 退款处理 ==
    alt 需要退款
        T -> S: 5. 操作订单退款
        note right: 在系统中\n执行退款操作
        
        S -> B: 6. 自动返回\n订单状态
        note right: 系统自动化\n状态同步
    end
    
    T -> B: 7. 返回处理结果
    note right: 处理结果\n反馈给蜂助手
    
else 其他问题
    B -> B: 3. 直接处理
    note right: 标准流程\n快速解决
end

== 结果反馈 ==
B -> C: 8. 联系客户告知\n处理结果
note right: 完成闭环\n客户满意度跟踪

@enduml
