# UML-MCP Server 配置说明

## ✅ 安装成功！

UML-MCP Server 已经成功安装在您的系统上：

**安装路径：** `C:\Users\<USER>\Documents\个人文档\dev\聊天对话日常\uml-mcp`

## 🔧 MCP 配置

### 对于 Claude Desktop

将以下配置添加到您的 Claude Desktop 配置文件中：

**Windows 路径：** `%APPDATA%\Claude\claude_desktop_config.json`

```json
{
  "mcpServers": {
    "UML-MCP-Server": {
      "command": "python",
      "args": [
        "C:\\Users\\<USER>\\Documents\\个人文档\\dev\\聊天对话日常\\uml-mcp\\mcp_server.py",
        "run"
      ],
      "env": {
        "UML_MCP_OUTPUT_DIR": "C:\\Users\\<USER>\\Documents\\个人文档\\dev\\聊天对话日常\\uml-output"
      }
    }
  }
}
```

### 对于 Cursor

将以下配置添加到您的 Cursor 设置中：

```json
{
  "mcpServers": {
    "UML-MCP-Server": {
      "command": "python",
      "args": [
        "C:\\Users\\<USER>\\Documents\\个人文档\\dev\\聊天对话日常\\uml-mcp\\mcp_server.py",
        "run"
      ],
      "env": {
        "UML_MCP_OUTPUT_DIR": "C:\\Users\\<USER>\\Documents\\个人文档\\dev\\聊天对话日常\\uml-output"
      }
    }
  }
}
```

## 📁 创建输出目录

让我为您创建输出目录：

```bash
mkdir "C:\Users\<USER>\Documents\个人文档\dev\聊天对话日常\uml-output"
```

## 🎯 支持的功能

UML-MCP Server 支持以下图表类型：

### UML 图表
- ✅ **类图** (Class Diagram)
- ✅ **时序图** (Sequence Diagram) 
- ✅ **活动图** (Activity Diagram)
- ✅ **用例图** (Use Case Diagram)
- ✅ **状态图** (State Diagram)
- ✅ **组件图** (Component Diagram)
- ✅ **部署图** (Deployment Diagram)
- ✅ **对象图** (Object Diagram)

### 其他图表
- ✅ **Mermaid 图表**
- ✅ **D2 图表**
- ✅ **Graphviz 图表**
- ✅ **ERD 图表**

## 🚀 使用方法

配置完成后，您可以在 Claude Desktop 或 Cursor 中直接使用：

```
请帮我生成一个客诉与退款机制的 PlantUML 时序图
```

或者：

```
请使用 UML-MCP 工具生成一个类图，显示客户、蜂助手客服、腾讯视频客服之间的关系
```

## 🔍 测试配置

要测试服务器是否正常工作，可以运行：

```bash
cd "C:\Users\<USER>\Documents\个人文档\dev\聊天对话日常\uml-mcp"
python mcp_server.py info
```

## 💡 优势

使用 UML-MCP Server 的优势：

1. **直接集成**：在 AI 助手中直接生成图表
2. **多种格式**：支持 PlantUML、Mermaid、D2 等
3. **自动保存**：生成的图表自动保存到指定目录
4. **高质量输出**：支持 SVG、PNG、PDF 等格式

现在您就可以使用专业的 UML 工具来生成您的客诉退款流程图了！🎉
