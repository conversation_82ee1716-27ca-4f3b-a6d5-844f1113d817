# 客诉与退款机制时序图

```mermaid
sequenceDiagram
    participant 客户 as 客户
    participant 蜂助手客服 as 蜂助手客服
    participant 腾讯视频客服 as 腾讯视频客服
    participant 系统 as 系统

    Note over 客户, 系统: 客诉与退款处理流程

    客户->>蜂助手客服: 提出客诉问题
    蜂助手客服->>蜂助手客服: 整理客户诉求
    
    alt 涉及腾讯视频权益查询和联合会员退款
        蜂助手客服->>腾讯视频客服: 通过在线文档通知问题
        腾讯视频客服->>腾讯视频客服: 处理客诉问题
        
        alt 需要退款
            腾讯视频客服->>系统: 操作订单退款
            系统->>蜂助手客服: 自动返回订单状态
        end
        
        腾讯视频客服->>蜂助手客服: 返回处理结果
    else 其他问题
        蜂助手客服->>蜂助手客服: 直接处理
    end
    
    蜂助手客服->>客户: 联系客户告知处理结果
    Note over 客户, 蜂助手客服: 客诉处理闭环完成
```

## 使用说明

您可以使用以下方式生成图片：

1. **在线工具**：
   - 访问 https://mermaid.live/
   - 复制上面的mermaid代码
   - 粘贴到编辑器中
   - 点击下载按钮保存为PNG/SVG

2. **VS Code插件**：
   - 安装 "Mermaid Preview" 插件
   - 打开此文件
   - 右键选择 "Preview Mermaid"

3. **命令行工具**：
   ```bash
   npm install -g @mermaid-js/mermaid-cli
   mmdc -i 时序图.md -o 时序图.png
   ```
