# 用户旅程图（水平格式 - 传统Journey图）

```mermaid
journey
    title 客户客诉与退款用户旅程
    section 问题发现阶段
      遇到问题: 1: 客户
      决定投诉: 2: 客户
    section 客诉提交阶段
      联系蜂助手客服: 3: 客户
      描述问题详情: 4: 客户
    section 问题处理阶段
      等待客服处理: 2: 客户
      客服整理诉求: 5: 蜂助手客服
      转交腾讯视频客服: 5: 蜂助手客服, 腾讯视频客服
      腾讯视频客服处理: 5: 腾讯视频客服
    section 退款处理阶段
      执行退款操作: 5: 腾讯视频客服
      系统自动处理: 5: 系统
      状态同步: 5: 系统, 蜂助手客服
    section 结果反馈阶段
      接收处理结果: 4: 客户
      确认问题解决: 5: 客户
    section 闭环完成阶段
      客诉处理完成: 5: 客户, 蜂助手客服
```

## 说明

这是传统的Mermaid journey图表，从左到右显示。数字代表满意度评分（1-5分）。

## 使用说明

您可以使用以下方式生成图片：

1. **在线工具**：
   - 访问 https://mermaid.live/
   - 复制上面的mermaid代码
   - 粘贴到编辑器中
   - 点击下载按钮保存为PNG/SVG

2. **VS Code插件**：
   - 安装 "Mermaid Preview" 插件
   - 打开此文件
   - 右键选择 "Preview Mermaid"

3. **命令行工具**：
   ```bash
   npm install -g @mermaid-js/mermaid-cli
   mmdc -i 用户旅程图_水平.md -o 用户旅程图_水平.png
   ```
