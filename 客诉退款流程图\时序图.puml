@startuml 客诉与退款机制时序图

!theme plain
skinparam backgroundColor white
skinparam participant {
    BackgroundColor white
    BorderColor black
    BorderThickness 2
}

skinparam sequence {
    ArrowColor black
    ArrowThickness 2
    LifeLineBackgroundColor white
    LifeLineBorderColor black
    LifeLineBorderThickness 2
}

skinparam note {
    BackgroundColor #FFFACD
    BorderColor #DAA520
}

title 客诉与退款机制 - 时序图\n<size:12><color:gray>展示各方交互的时间顺序和消息传递</color></size>

participant "👤\n客户" as Customer
participant "🐝\n蜂助手客服" as BeeService  
participant "📺\n腾讯视频客服" as TencentService
participant "⚙️\n系统" as System

== 问题发现与提交 ==
Customer -> BeeService: 1. 提出客诉问题
note right: 客户遇到问题后\n主动联系客服

BeeService -> BeeService: 2. 整理客户诉求
note right: 记录问题详情\n分析问题类型

== 问题分析与分发 ==
alt 涉及腾讯视频权益查询和联合会员退款
    BeeService -> TencentService: 3. 通过在线文档通知问题
    note right: 跨部门协作\n信息准确传递
    
    TencentService -> TencentService: 4. 处理客诉问题
    note right: 专业团队\n深入分析问题
    
    == 退款处理 ==
    alt 需要退款
        TencentService -> System: 5. 操作订单退款
        note right: 在系统中\n执行退款操作
        
        System -> BeeService: 6. 自动返回订单状态
        note right: 系统自动化\n状态同步
    end
    
    TencentService -> BeeService: 7. 返回处理结果
    note right: 处理结果\n反馈给蜂助手
    
else 其他问题
    BeeService -> BeeService: 3. 直接处理
    note right: 标准流程\n快速解决
end

== 结果反馈 ==
BeeService -> Customer: 8. 联系客户告知处理结果
note right: 完成闭环\n客户满意度跟踪

@enduml
