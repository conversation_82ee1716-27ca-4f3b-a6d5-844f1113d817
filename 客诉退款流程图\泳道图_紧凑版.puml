@startuml 客诉与退款机制泳道图_垂直流向紧凑版

!theme plain
skinparam backgroundColor white
skinparam dpi 150
skinparam swimlaneWidth 120
skinparam swimlaneBorderColor black
skinparam swimlaneBorderThickness 2
skinparam swimlaneTitleBackgroundColor #E8E8E8
skinparam swimlaneTitleFontColor black
skinparam swimlaneTitleFontSize 10
skinparam swimlaneTitleFontStyle bold

skinparam activity {
    BackgroundColor white
    BorderColor black
    BorderThickness 1
    FontColor black
    FontSize 9
    Padding 4
}

skinparam diamond {
    BackgroundColor #FFE6CC
    BorderColor #FF9900
    BorderThickness 1
    FontColor black
    FontSize 8
}

skinparam start {
    BackgroundColor #E6F3FF
    BorderColor #0066CC
    BorderThickness 2
}

skinparam stop {
    BackgroundColor #E6FFE6
    BorderColor #009900
    BorderThickness 2
}

skinparam arrow {
    Color black
    Thickness 1
}

title 客诉与退款机制 - 泳道图\n<size:10><color:gray>垂直流向，紧凑布局</color></size>

|👤客户|
start
:遇到问题;
:决定投诉;
:联系客服;

|🐝蜂助手客服|
:接收客诉;
:整理诉求;
if (问题类型?) then (腾讯视频相关)
  :通知腾讯视频客服;
else (一般问题)
  :直接处理;
  :联系客户告知结果;
  stop
endif

|📺腾讯视频客服|
:接收问题通知;
:分析处理方案;
if (是否需要退款?) then (需要退款)
  :发起退款操作;
else (其他处理)
  :执行其他解决方案;
  :返回处理结果;
  jump to 反馈客户
endif

|⚙️系统|
:自动处理退款;
:更新订单状态;

|📺腾讯视频客服|
:确认退款完成;
:返回处理结果;

|🐝蜂助手客服|
label 反馈客户
:接收处理结果;
:联系客户告知结果;

|👤客户|
:接收处理结果;
:确认问题解决;

|🐝蜂助手客服|
:完成客诉处理;
stop

@enduml
