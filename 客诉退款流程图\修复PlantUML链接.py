#!/usr/bin/env python3
"""
修复 PlantUML URL 编码问题
自动添加 ~1 前缀以支持 HUFFMAN 编码
"""

import re
import sys
from pathlib import Path

def fix_plantuml_url(url):
    """
    修复 PlantUML URL，添加 ~1 前缀以支持 HUFFMAN 编码
    
    Args:
        url (str): 原始的 PlantUML URL
        
    Returns:
        str: 修复后的 URL
    """
    # PlantUML URL 模式
    pattern = r'(http://www\.plantuml\.com/plantuml/\w+/)([^~][A-Za-z0-9+/=_-]+)'
    
    def add_huffman_prefix(match):
        base_url = match.group(1)
        encoded_data = match.group(2)
        return f"{base_url}~1{encoded_data}"
    
    fixed_url = re.sub(pattern, add_huffman_prefix, url)
    return fixed_url

def fix_plantuml_urls_in_text(text):
    """
    修复文本中的所有 PlantUML URL
    
    Args:
        text (str): 包含 PlantUML URL 的文本
        
    Returns:
        str: 修复后的文本
    """
    # 查找所有 PlantUML URL
    pattern = r'http://www\.plantuml\.com/plantuml/\w+/[A-Za-z0-9+/=_-]+'
    
    def fix_url(match):
        original_url = match.group(0)
        fixed_url = fix_plantuml_url(original_url)
        return fixed_url
    
    fixed_text = re.sub(pattern, fix_url, text)
    return fixed_text

def main():
    """主函数"""
    
    print("🔧 PlantUML URL 修复工具")
    print("=" * 40)
    
    # 测试URL
    test_url = "http://www.plantuml.com/plantuml/svg/jZVdT9pQGMfvz6c4izd6sQRZYiZczGjkC8xk19XW0VhaU-qMG0vATUUUhYDOCdPI8GWLqzMYZDLdd1k4p6dX20fYaQ8vpRQymhD6vPyf3_Oc54SJqMap2lJEgkgvkavNRm3HjMfx5QMu3qFkFVcqZiKHCg8APNLCQkSAixInyiC6IMqLnMpF4Cw3t_BSVZZkfkqRFBUuh0VNcPijy2JE4mThhchrYej3-zx8k4rKCyrLn5WoYN-YmbA4tyAL0Sj0e8TMiJokTLqAhqafWk-_8JAia4Mrt8Oei68FONq3sB2irUgCnFUkHjjCuDlNfCVqK_ANgPTjJmQjsz09g-hYO62P2lY3eMvGKEfBWycBL3IRReb7AAyFQtNjU1M9CNQ-Pu7zeUL4_wPC1w1hr1o_hOmx0JNQqBfB5xvrRnMiuOSVxQHqtEUv9QENdqlzqqosN-XdHbuSNGsbvO8TfAzJ7QVJbJN39467Fft7nC1bGTh5GwP2nEDATGyg5Hfzg26WDoIggNYrSD_EqT2qSl9JIm9U6qS4ilJf8OaWlVtMB22pzCfossdAAO-c4nyVQdF0vHdjZNZZRWrB16s0V5yHw6yecV1HR1soWcb7356NQHr1ZThM1h6IfkXO181S1ijU0FplxG4_YMYPjeNTp7uFY7ntL4qVu4O9ITGmwOhYbabGcgOo_J5xUsjWKNqiFi8-uEKZM7MYJ2cJNuk2LzMyRlZl85ycbDfPI5du3BeDbWf7R-z34cc_tV1Ip2vUj2OdbLLxFaUuGBDTCDqcv_KocET0EkrvGakqjie8pAcOQZCiAhxu1NI93Git2qjvs8p4_wf6ucvEBZkX5zvjaEKwOKOew0fF7hPwWgzH-D0SW0xxkrxk428euVG4oUksIwgYSGsp2VahbIoeY0vNteKeFQPGiU70MqtDzj_TjQ8C61YDMEEr0D-pfw=="
    
    print("🔍 原始URL:")
    print(test_url)
    print()
    
    fixed_url = fix_plantuml_url(test_url)
    
    print("✅ 修复后的URL:")
    print(fixed_url)
    print()
    
    print("🎯 修复说明:")
    print("- 添加了 ~1 前缀以支持 HUFFMAN 编码")
    print("- 现在可以正常在 PlantUML 网站上查看")
    print()
    
    # 检查是否有命令行参数
    if len(sys.argv) > 1:
        input_url = sys.argv[1]
        print(f"🔧 修复用户提供的URL:")
        print(f"原始: {input_url}")
        print(f"修复: {fix_plantuml_url(input_url)}")
    
    print("💡 使用方法:")
    print("python 修复PlantUML链接.py [URL]")

if __name__ == "__main__":
    main()
