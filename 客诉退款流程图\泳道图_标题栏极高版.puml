@startuml 客诉与退款机制泳道图_标题栏极高版

!theme plain
skinparam backgroundColor white
skinparam dpi 150
skinparam swimlaneWidth 120
skinparam swimlaneBorderColor black
skinparam swimlaneBorderThickness 2
skinparam swimlaneTitleBackgroundColor #E8E8E8
skinparam swimlaneTitleFontColor black
skinparam swimlaneTitleFontSize 12
skinparam swimlaneTitleFontStyle bold
skinparam swimlaneTitleHeight 80

skinparam activity {
    BackgroundColor white
    BorderColor black
    BorderThickness 1
    FontColor black
    FontSize 9
    Padding 4
}

skinparam diamond {
    BackgroundColor #FFE6CC
    BorderColor #FF9900
    BorderThickness 1
    FontColor black
    FontSize 8
}

skinparam start {
    BackgroundColor #E6F3FF
    BorderColor #0066CC
    BorderThickness 2
}

skinparam stop {
    BackgroundColor #E6FFE6
    BorderColor #009900
    BorderThickness 2
}

skinparam arrow {
    Color black
    Thickness 1
}

title 客诉与退款机制 - 紧凑版泳道图\n<size:10><color:gray>标题栏极高版，适合A4打印</color></size>

|👤客户|
start
:遇到问题;
:决定投诉;

|🐝蜂助手客服|
:接收客诉;
:整理诉求;

if (问题类型?) then (腾讯视频相关)
    :通知腾讯视频客服;
    
    |📺腾讯视频客服|
    :接收问题通知;
    :分析处理方案;
    
    if (是否需要退款?) then (需要退款)
        :发起退款操作;
        
        |⚙️系统|
        :自动处理退款;
        :更新订单状态;
        
        |📺腾讯视频客服|
        :确认退款完成;
    else (其他处理)
        :执行其他解决方案;
    endif
    
    :返回处理结果;
    
    |🐝蜂助手客服|
    :接收处理结果;
    
else (一般问题)
    :直接处理;
endif

:联系客户告知结果;

|👤客户|
:接收处理结果;
:确认问题解决;

|🐝蜂助手客服|
:完成客诉处理;
stop

@enduml
