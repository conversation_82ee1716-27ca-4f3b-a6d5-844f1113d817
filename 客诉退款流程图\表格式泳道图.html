<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>客诉与退款机制 - 表格式泳道图</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        
        .swimlane-container {
            background: white;
            border: 2px solid #333;
            border-collapse: collapse;
            width: 100%;
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .swimlane-table {
            width: 100%;
            border-collapse: collapse;
            table-layout: fixed;
        }
        
        .header-row th {
            background-color: #e8e8e8;
            border: 1px solid #333;
            padding: 10px;
            text-align: center;
            font-weight: bold;
            height: 40px;
        }
        
        .phase-header {
            background-color: #f0f0f0;
            border: 1px solid #333;
            padding: 10px;
            text-align: center;
            font-weight: bold;
            writing-mode: vertical-rl;
            text-orientation: mixed;
            width: 60px;
        }
        
        .swimlane-cell {
            border: 1px solid #333;
            padding: 8px;
            vertical-align: top;
            height: 120px;
            position: relative;
        }
        
        .process-box {
            background: #fff;
            border: 1px solid #666;
            border-radius: 4px;
            padding: 6px;
            margin: 2px;
            text-align: center;
            font-size: 12px;
            position: relative;
        }
        
        .decision-box {
            background: #ffe6cc;
            border: 1px solid #ff9900;
            border-radius: 8px;
            padding: 6px;
            margin: 2px;
            text-align: center;
            font-size: 12px;
        }
        
        .start-box {
            background: #e6f3ff;
            border: 1px solid #0066cc;
            border-radius: 20px;
            padding: 6px;
            margin: 2px;
            text-align: center;
            font-size: 12px;
        }
        
        .end-box {
            background: #e6ffe6;
            border: 1px solid #009900;
            border-radius: 4px;
            padding: 6px;
            margin: 2px;
            text-align: center;
            font-size: 12px;
        }
        
        .arrow {
            position: absolute;
            font-size: 16px;
            color: #333;
        }
        
        .customer { background-color: #ffe6e6; }
        .bee-service { background-color: #fff3e0; }
        .tencent-service { background-color: #f3e5f5; }
        .system { background-color: #e8f5e8; }
        
        .title {
            text-align: center;
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 20px;
            color: #333;
        }
    </style>
</head>
<body>
    <div class="title">客诉与退款机制 - 表格式泳道图</div>
    
    <div class="swimlane-container">
        <table class="swimlane-table">
            <!-- 表头 -->
            <tr class="header-row">
                <th style="width: 60px;">阶段</th>
                <th style="width: 160px;">👤 客户</th>
                <th style="width: 160px;">🐝 蜂助手客服</th>
                <th style="width: 160px;">📺 腾讯视频客服</th>
                <th style="width: 160px;">⚙️ 系统</th>
            </tr>
            
            <!-- 问题发现阶段 -->
            <tr>
                <td class="phase-header">问题发现</td>
                <td class="swimlane-cell customer">
                    <div class="start-box">遇到问题</div>
                    <div class="process-box">决定投诉</div>
                </td>
                <td class="swimlane-cell bee-service"></td>
                <td class="swimlane-cell tencent-service"></td>
                <td class="swimlane-cell system"></td>
            </tr>
            
            <!-- 客诉提交阶段 -->
            <tr>
                <td class="phase-header">客诉提交</td>
                <td class="swimlane-cell customer">
                    <div class="process-box">联系蜂助手客服</div>
                    <div class="process-box">描述问题详情</div>
                </td>
                <td class="swimlane-cell bee-service">
                    <div class="process-box">接收客诉</div>
                    <div class="process-box">整理客户诉求</div>
                </td>
                <td class="swimlane-cell tencent-service"></td>
                <td class="swimlane-cell system"></td>
            </tr>
            
            <!-- 问题分析阶段 -->
            <tr>
                <td class="phase-header">问题分析</td>
                <td class="swimlane-cell customer">
                    <div class="process-box">等待处理结果</div>
                </td>
                <td class="swimlane-cell bee-service">
                    <div class="decision-box">判断问题类型</div>
                    <div class="process-box">通知腾讯视频客服</div>
                </td>
                <td class="swimlane-cell tencent-service">
                    <div class="process-box">接收问题通知</div>
                    <div class="process-box">分析处理方案</div>
                </td>
                <td class="swimlane-cell system"></td>
            </tr>
            
            <!-- 处理执行阶段 -->
            <tr>
                <td class="phase-header">处理执行</td>
                <td class="swimlane-cell customer"></td>
                <td class="swimlane-cell bee-service">
                    <div class="process-box">接收处理结果</div>
                </td>
                <td class="swimlane-cell tencent-service">
                    <div class="decision-box">是否需要退款？</div>
                    <div class="process-box">执行退款操作</div>
                </td>
                <td class="swimlane-cell system">
                    <div class="process-box">自动处理退款</div>
                    <div class="process-box">返回订单状态</div>
                </td>
            </tr>
            
            <!-- 结果反馈阶段 -->
            <tr>
                <td class="phase-header">结果反馈</td>
                <td class="swimlane-cell customer">
                    <div class="process-box">接收处理结果</div>
                    <div class="end-box">确认问题解决</div>
                </td>
                <td class="swimlane-cell bee-service">
                    <div class="process-box">联系客户告知结果</div>
                    <div class="end-box">客诉处理完成</div>
                </td>
                <td class="swimlane-cell tencent-service"></td>
                <td class="swimlane-cell system"></td>
            </tr>
        </table>
    </div>
    
    <div style="margin-top: 20px; text-align: center; color: #666; font-size: 12px;">
        <p>说明：此图表使用HTML+CSS实现，可以精确控制表格布局和样式</p>
        <p>您可以在浏览器中打开此文件，然后截图或打印为PDF</p>
    </div>
</body>
</html>
