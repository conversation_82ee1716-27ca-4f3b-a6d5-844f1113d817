# PlantUML 测试说明

## 🔧 问题修复

我发现之前的美化版本语法过于复杂，导致解析错误。现在我创建了一个**简洁美化版**：

**文件：** `PlantUML泳道图_简洁美化版.puml`

## ✅ 修复的问题

1. **移除了复杂的partition语法** - 避免解析错误
2. **简化了颜色和样式定义** - 提高兼容性
3. **保留了完整的边界线** - 满足您的需求
4. **优化了流程逻辑** - 更清晰的条件分支

## 🎨 美化特点

- ✅ **完整边界线**：每个泳道都有2像素的黑色边界
- ✅ **清晰标题**：每个泳道标题有灰色背景
- ✅ **颜色区分**：
  - 决策框：橙色背景
  - 开始框：蓝色背景  
  - 结束框：绿色背景
  - 活动框：白色背景，黑色边框
- ✅ **粗箭头**：2像素粗的黑色箭头

## 🚀 快速测试

### 方法1：在线测试（推荐）
1. 访问：http://www.plantuml.com/plantuml/
2. 复制下面的代码：

```plantuml
@startuml 客诉与退款机制泳道图

!theme plain
skinparam backgroundColor white
skinparam swimlaneWidth 200
skinparam swimlaneBorderColor black
skinparam swimlaneBorderThickness 2
skinparam swimlaneTitleBackgroundColor #E8E8E8

title 客诉与退款机制 - 跨职能泳道图

|👤 客户|
start
:遇到问题;
:决定投诉;
:联系蜂助手客服;

|🐝 蜂助手客服|
:接收客诉;
:整理客户诉求;

if (问题类型判断?) then (腾讯视频相关)
    :通知腾讯视频客服;
    
    |📺 腾讯视频客服|
    :接收问题通知;
    :处理客诉问题;
    
    if (是否需要退款?) then (需要)
        :执行退款操作;
        
        |⚙️ 系统|
        :自动处理退款;
        :返回订单状态;
        
        |📺 腾讯视频客服|
    else (不需要)
        :其他处理方式;
    endif
    
    :返回处理结果;
    
    |🐝 蜂助手客服|
    :接收处理结果;
    
else (一般问题)
    :直接处理;
endif

:联系客户告知结果;

|👤 客户|
:接收处理结果;
:确认问题解决;
stop

@enduml
```

3. 点击"Submit"生成图片
4. 右键保存图片

### 方法2：VS Code插件
1. 安装"PlantUML"插件
2. 打开`PlantUML泳道图_简洁美化版.puml`
3. 按`Alt+D`预览

## 📊 效果预期

这个版本应该能生成：
- 4个清晰的泳道（客户、蜂助手客服、腾讯视频客服、系统）
- 每个泳道都有完整的黑色边界线
- 清晰的流程分支和决策点
- 专业的配色方案

## 💡 如果还有问题

如果这个版本还有问题，我建议：
1. **使用Draw.io** - 最稳定可靠的选择
2. **使用HTML版本** - 我已经创建的表格式版本
3. **告诉我具体错误信息** - 我可以进一步调试

现在请试试这个简洁美化版，应该能正常工作了！🎉
