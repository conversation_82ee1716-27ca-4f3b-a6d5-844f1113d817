@startuml 客诉与退款机制时序图_紧凑版

!theme plain
skinparam backgroundColor white
skinparam dpi 150
skinparam maxMessageSize 120

skinparam participant {
    BackgroundColor white
    BorderColor black
    BorderThickness 2
    FontSize 10
    Padding 6
}

skinparam sequence {
    ArrowColor black
    ArrowThickness 2
    LifeLineBackgroundColor white
    LifeLineBorderColor black
    LifeLineBorderThickness 2
    ParticipantPadding 15
    BoxPadding 8
}

skinparam note {
    BackgroundColor #FFFACD
    BorderColor #DAA520
    FontSize 9
}

title 客诉与退款机制 - 时序图\n<size:10><color:gray>紧凑版，适合A4打印</color></size>

participant "👤\n客户" as C
participant "🐝\n蜂助手" as B
participant "📺\n腾讯视频" as T
participant "⚙️\n系统" as S

== 问题发现 ==
C -> B: 提出客诉问题
B -> B: 整理客户诉求

== 问题分析 ==
alt 腾讯视频相关
    B -> T: 通知问题
    T -> T: 处理客诉
    
    == 退款处理 ==
    alt 需要退款
        T -> S: 操作退款
        S -> B: 返回状态
    end
    
    T -> B: 返回结果
    
else 一般问题
    B -> B: 直接处理
end

== 结果反馈 ==
B -> C: 告知结果

@enduml
