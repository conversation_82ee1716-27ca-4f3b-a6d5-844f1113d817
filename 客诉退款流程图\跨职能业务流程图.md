# 跨职能业务流程图（垂直格式）

```mermaid
flowchart TD
    A[客户提出客诉] --> B{客诉类型判断}
    
    B -->|涉及腾讯视频权益/退款| C[蜂助手客服整理诉求]
    B -->|其他问题| D[蜂助手客服直接处理]
    
    C --> E[通过在线文档通知腾讯视频客服]
    E --> F[腾讯视频客服处理问题]
    
    F --> G{是否需要退款}
    G -->|是| H[腾讯视频客服操作退款]
    G -->|否| I[其他处理方式]
    
    H --> J[系统自动返回订单状态]
    I --> K[腾讯视频客服返回处理结果]
    J --> K
    
    K --> L[蜂助手客服接收处理结果]
    D --> M[蜂助手客服联系客户]
    L --> M
    
    M --> N[客诉处理闭环完成]
    
    style A fill:#e1f5fe
    style N fill:#c8e6c9
    style H fill:#fff3e0
    style J fill:#f3e5f5
```

## 使用说明

您可以使用以下方式生成图片：

1. **在线工具**：
   - 访问 https://mermaid.live/
   - 复制上面的mermaid代码
   - 粘贴到编辑器中
   - 点击下载按钮保存为PNG/SVG

2. **VS Code插件**：
   - 安装 "Mermaid Preview" 插件
   - 打开此文件
   - 右键选择 "Preview Mermaid"

3. **命令行工具**：
   ```bash
   npm install -g @mermaid-js/mermaid-cli
   mmdc -i 跨职能业务流程图.md -o 跨职能业务流程图.png
   ```
