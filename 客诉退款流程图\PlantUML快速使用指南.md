# PlantUML 美化版泳道图使用指南

## 🎯 我为您创建了3个PlantUML版本

1. **PlantUML泳道图.puml** - 基础版本
2. **PlantUML泳道图_美化版.puml** - 丰富的视觉效果和颜色 🌟
3. **PlantUML泳道图_表格式.puml** - 更接近表格式布局

## 🚀 快速生成方法

### 方法一：在线生成（最简单）
1. 访问：http://www.plantuml.com/plantuml/
2. 复制任意一个.puml文件的内容
3. 粘贴到网页编辑器中
4. 点击"Submit"生成图片
5. 右键保存图片

### 方法二：VS Code插件
1. 安装"PlantUML"插件
2. 打开.puml文件
3. 按 `Alt+D` 预览
4. 右键导出为PNG/SVG

### 方法三：命令行（需要Java）
```bash
# 下载plantuml.jar到项目文件夹
# 然后运行：
java -jar plantuml.jar PlantUML泳道图_美化版.puml
```

## 🎨 美化版特点

### 视觉增强：
- ✅ **完整边界线**：每个泳道都有清晰的边界
- ✅ **阶段分隔**：用partition创建水平分隔线
- ✅ **颜色区分**：不同阶段用不同背景色
- ✅ **图标装饰**：使用emoji增强可读性
- ✅ **详细说明**：每个步骤都有小字说明
- ✅ **注释说明**：底部有流程说明

### 布局特点：
- 📊 **表格式结构**：清晰的行列布局
- 🏊‍♀️ **完整泳道**：每个参与方都有独立区域
- 🔄 **流程清晰**：箭头和连接线明确
- 📝 **职责明确**：每个角色的任务一目了然

## 📋 推荐使用

**最推荐：PlantUML泳道图_美化版.puml**
- 视觉效果最佳
- 信息最完整
- 专业度最高

**如果需要简洁版本：PlantUML泳道图_表格式.puml**
- 更接近传统表格样式
- 布局更紧凑
- 适合打印

## 🔧 自定义修改

如果您想调整样式，可以修改这些参数：

```plantuml
' 泳道宽度
skinparam swimlaneWidth 240

' 边界线颜色和粗细
skinparam swimlaneBorderColor #333333
skinparam swimlaneBorderThickness 3

' 背景颜色
skinparam backgroundColor #FAFAFA

' 字体大小
skinparam swimlaneTitleFontSize 14
```

## 💡 小贴士

1. **在线生成最方便**：不需要安装任何软件
2. **支持多种格式**：PNG、SVG、PDF等
3. **可以调整尺寸**：修改swimlaneWidth参数
4. **支持中文**：完美支持中文显示
5. **专业效果**：输出质量很高，适合商务使用

现在您就可以生成专业级的表格式泳道图了！🎉
