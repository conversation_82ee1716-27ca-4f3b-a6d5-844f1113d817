# 用户旅程图（垂直格式）

```mermaid
flowchart TD
    subgraph 阶段1 ["🔍 问题发现阶段"]
        A1[客户遇到问题<br/>😟 情绪：困惑/不满]
        A2[客户决定投诉<br/>😤 情绪：不满/期待解决]
    end
    
    subgraph 阶段2 ["📞 客诉提交阶段"]
        B1[联系蜂助手客服<br/>😐 情绪：期待]
        B2[向客服描述问题详情<br/>😐 情绪：期待帮助]
    end
    
    subgraph 阶段3 ["⏳ 问题处理阶段"]
        C1[客户等待客服处理<br/>😑 情绪：焦虑等待]
        C2[蜂助手客服整理诉求<br/>🔄 内部处理]
        C3[转交腾讯视频客服<br/>🔄 跨部门协作]
        C4[腾讯视频客服处理<br/>🔄 专业处理]
    end
    
    subgraph 阶段4 ["💰 退款处理阶段"]
        D1[腾讯视频客服执行退款<br/>🔄 系统操作]
        D2[系统自动处理退款<br/>⚙️ 自动化流程]
        D3[状态同步给蜂助手<br/>🔄 信息同步]
    end
    
    subgraph 阶段5 ["📢 结果反馈阶段"]
        E1[客户接收处理结果<br/>😊 情绪：期待确认]
        E2[客户确认问题解决<br/>😊 情绪：满意]
    end
    
    subgraph 阶段6 ["✅ 闭环完成阶段"]
        F1[客诉处理完成<br/>😊 情绪：满意/信任]
    end
    
    A1 --> A2
    A2 --> B1
    B1 --> B2
    B2 --> C1
    C1 --> C2
    C2 --> C3
    C3 --> C4
    C4 --> D1
    D1 --> D2
    D2 --> D3
    D3 --> E1
    E1 --> E2
    E2 --> F1
    
    style 阶段1 fill:#ffebee
    style 阶段2 fill:#e3f2fd
    style 阶段3 fill:#fff3e0
    style 阶段4 fill:#f3e5f5
    style 阶段5 fill:#e8f5e8
    style 阶段6 fill:#c8e6c9
```

## 使用说明

这个垂直用户旅程图包含了：
- 6个主要阶段，从上到下展示
- 每个阶段用不同颜色区分
- 包含客户情绪变化
- 显示内部处理流程

您可以使用以下方式生成图片：

1. **在线工具**：
   - 访问 https://mermaid.live/
   - 复制上面的mermaid代码
   - 粘贴到编辑器中
   - 点击下载按钮保存为PNG/SVG

2. **VS Code插件**：
   - 安装 "Mermaid Preview" 插件
   - 打开此文件
   - 右键选择 "Preview Mermaid"

3. **命令行工具**：
   ```bash
   npm install -g @mermaid-js/mermaid-cli
   mmdc -i 用户旅程图_垂直.md -o 用户旅程图_垂直.png
   ```
