# 客诉与退款机制泳道图（跨职能）

```mermaid
flowchart TD
    subgraph 客户泳道 ["👤 客户"]
        A1[遇到问题]
        A2[提出客诉]
        A3[等待处理结果]
        A4[接收处理结果]
        A5[确认问题解决]
    end
    
    subgraph 蜂助手客服泳道 ["🐝 蜂助手客服"]
        B1[接收客诉]
        B2[整理客户诉求]
        B3{判断问题类型}
        B4[直接处理一般问题]
        B5[通过在线文档<br/>通知腾讯视频客服]
        B6[接收腾讯视频<br/>处理结果]
        B7[联系客户<br/>告知结果]
    end
    
    subgraph 腾讯视频客服泳道 ["📺 腾讯视频客服"]
        C1[接收问题通知]
        C2[处理客诉问题]
        C3{是否需要退款}
        C4[执行退款操作]
        C5[其他处理方式]
        C6[返回处理结果<br/>给蜂助手]
    end
    
    subgraph 系统泳道 ["⚙️ 系统"]
        D1[自动处理退款]
        D2[返回订单状态]
    end
    
    %% 流程连接
    A1 --> A2
    A2 --> B1
    B1 --> B2
    B2 --> B3
    
    B3 -->|一般问题| B4
    B3 -->|腾讯视频相关| B5
    
    B4 --> B7
    B5 --> C1
    C1 --> C2
    C2 --> C3
    
    C3 -->|需要退款| C4
    C3 -->|其他处理| C5
    
    C4 --> D1
    D1 --> D2
    D2 --> B6
    C5 --> C6
    C6 --> B6
    
    B6 --> B7
    B7 --> A3
    A3 --> A4
    A4 --> A5
    
    %% 样式设置
    style 客户泳道 fill:#e3f2fd
    style 蜂助手客服泳道 fill:#fff3e0
    style 腾讯视频客服泳道 fill:#f3e5f5
    style 系统泳道 fill:#e8f5e8
    
    style A1 fill:#ffcdd2
    style A5 fill:#c8e6c9
    style C4 fill:#ffe0b2
    style D1 fill:#d1c4e9
```

## 泳道图说明

这个泳道图清晰地展示了：

1. **客户泳道**：客户的整个体验流程
2. **蜂助手客服泳道**：蜂助手客服的处理流程和决策点
3. **腾讯视频客服泳道**：腾讯视频客服的专业处理流程
4. **系统泳道**：自动化系统处理流程

### 优势：
- 清晰显示各方职责边界
- 突出跨部门协作点
- 易于识别流程瓶颈
- 便于优化和改进

## 使用说明

您可以使用以下方式生成图片：

1. **在线工具**：
   - 访问 https://mermaid.live/
   - 复制上面的mermaid代码
   - 粘贴到编辑器中
   - 点击下载按钮保存为PNG/SVG

2. **VS Code插件**：
   - 安装 "Mermaid Preview" 插件
   - 打开此文件
   - 右键选择 "Preview Mermaid"

3. **命令行工具**：
   ```bash
   npm install -g @mermaid-js/mermaid-cli
   mmdc -i 泳道图_跨职能.md -o 泳道图_跨职能.png
   ```
