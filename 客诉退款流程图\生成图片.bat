@echo off
echo 正在生成客诉退款流程图...

REM 检查是否安装了mermaid-cli
where mmdc >nul 2>nul
if %errorlevel% neq 0 (
    echo 错误：未找到 mermaid-cli 工具
    echo 请先安装：npm install -g @mermaid-js/mermaid-cli
    pause
    exit /b 1
)

REM 创建输出目录
if not exist "images" mkdir images

REM 生成时序图
echo 生成时序图...
mmdc -i "时序图.md" -o "images/时序图.png" -t default -b white

REM 生成跨职能业务流程图
echo 生成跨职能业务流程图...
mmdc -i "跨职能业务流程图.md" -o "images/跨职能业务流程图.png" -t default -b white

REM 生成用户旅程图（垂直）
echo 生成用户旅程图（垂直）...
mmdc -i "用户旅程图_垂直.md" -o "images/用户旅程图_垂直.png" -t default -b white

REM 生成用户旅程图（水平）
echo 生成用户旅程图（水平）...
mmdc -i "用户旅程图_水平.md" -o "images/用户旅程图_水平.png" -t default -b white

REM 生成泳道图（跨职能）
echo 生成泳道图（跨职能）...
mmdc -i "泳道图_跨职能.md" -o "images/泳道图_跨职能.png" -t default -b white

REM 检查是否有PlantUML
echo.
echo 检查PlantUML支持...
where java >nul 2>nul
if %errorlevel% equ 0 (
    if exist "plantuml.jar" (
        echo 生成PlantUML泳道图...
        java -jar plantuml.jar "PlantUML泳道图_美化版.puml" -o images
        java -jar plantuml.jar "PlantUML泳道图_表格式.puml" -o images
        echo PlantUML图表已生成
    ) else (
        echo 提示：如需生成PlantUML图表，请下载plantuml.jar到当前目录
        echo 下载地址：https://plantuml.com/download
    )
) else (
    echo 提示：如需生成PlantUML图表，请安装Java运行环境
)

echo.
echo 所有Mermaid图片已生成完成！
echo 图片保存在 images 文件夹中：
echo - 时序图.png
echo - 跨职能业务流程图.png
echo - 用户旅程图_垂直.png
echo - 用户旅程图_水平.png
echo - 泳道图_跨职能.png
echo.
echo 其他文件：
echo - 表格式泳道图.html (用浏览器打开)
echo - PlantUML泳道图_美化版.puml (需要PlantUML工具)
echo - PlantUML泳道图_表格式.puml (需要PlantUML工具)
echo.
echo 在线生成PlantUML: http://www.plantuml.com/plantuml/
echo.
pause
