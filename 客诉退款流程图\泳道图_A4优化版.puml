@startuml 客诉与退款机制泳道图_A4优化版

!theme plain
skinparam backgroundColor white
skinparam dpi 150
skinparam swimlaneWidth 160
skinparam swimlaneBorderColor black
skinparam swimlaneBorderThickness 2
skinparam swimlaneTitleBackgroundColor #E8E8E8
skinparam swimlaneTitleFontColor black
skinparam swimlaneTitleFontSize 12
skinparam swimlaneTitleFontStyle bold

skinparam activity {
    BackgroundColor white
    BorderColor black
    BorderThickness 2
    FontColor black
    FontSize 11
    Padding 8
}

skinparam diamond {
    BackgroundColor #FFE6CC
    BorderColor #FF9900
    BorderThickness 2
    FontColor black
    FontSize 10
}

skinparam start {
    BackgroundColor #E6F3FF
    BorderColor #0066CC
    BorderThickness 2
}

skinparam stop {
    BackgroundColor #E6FFE6
    BorderColor #009900
    BorderThickness 2
}

skinparam arrow {
    Color black
    Thickness 2
}

title 客诉与退款机制 - 泳道图\n<size:11><color:gray>展示各部门职责分工和跨职能协作</color></size>

|👤 客户|
start
:🔴 遇到问题;
note right: 发现服务异常\n或权益问题
:📞 决定投诉;
note right: 选择联系\n客服解决

|🐝 蜂助手客服|
:📋 接收客诉;
note right: 记录客户\n基本信息
:📝 整理客户诉求;
note right: 分析问题详情\n和影响范围

if (❓ 问题类型\n判断) then (腾讯视频相关)
    :📤 通知腾讯\n视频客服;
    note right: 通过在线文档\n传递问题信息
    
    |📺 腾讯视频客服|
    :📥 接收问题\n通知;
    note right: 获取详细\n问题描述
    :🔧 分析处理\n方案;
    note right: 专业团队\n制定解决方案
    
    if (💳 是否需要\n退款?) then (需要退款)
        :💰 发起退款\n操作;
        note right: 在系统中\n提交退款申请
        
        |⚙️ 系统|
        :🔄 自动处理\n退款;
        note right: 系统自动执行\n退款流程
        :📊 更新订单\n状态;
        note right: 同步状态信息\n到各个系统
        
        |📺 腾讯视频客服|
        :✅ 确认退款\n完成;
        note right: 验证退款\n处理结果
    else (其他处理)
        :🔧 执行其他\n解决方案;
        note right: 补偿、重新开通\n或其他处理方式
    endif
    
    :📤 准备处理\n结果;
    note right: 整理完整的\n处理报告
    
    |🐝 蜂助手客服|
    :📥 接收处理\n结果;
    note right: 获取腾讯视频\n的处理结果
    
else (一般问题)
    :✅ 直接处理;
    note right: 按标准流程\n快速解决
endif

:📞 联系客户\n告知结果;
note right: 电话或消息\n通知处理结果

|👤 客户|
:📨 接收处理\n结果;
note right: 了解最终\n处理情况
:✅ 确认问题\n解决;
note right: 验证问题是否\n彻底解决

|🐝 蜂助手客服|
:🎯 完成客诉\n处理;
note right: 记录处理结果\n闭环完成
stop

@enduml
